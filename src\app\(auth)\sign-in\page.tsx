"use client";

import { appName } from "@/config/constant";
import { signInWithGoogle, signInWithEmail } from "@/lib/auth";
import { Box, Button, Divider, Paper, TextField, Typography } from "@mui/material";
import { ChromeIcon } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const router = useRouter();

  const handleSignIn = async () => {
    await signInWithEmail(email, password);
  };

  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          {appName}
        </Typography>
        <Typography color="text.secondary" align="center" mb={2}>
          Sign in to your account
        </Typography>
        <form onSubmit={handleSignIn}>
          <TextField required label="Email" variant="outlined" fullWidth margin="normal" value={email} onChange={e => setEmail(e.target.value)} />
          <TextField 
            required 
            label="Password" 
            type="password" 
            variant="outlined" 
            fullWidth 
            margin="normal" 
            value={password} 
            onChange={e => setPassword(e.target.value)} 
          />
          <Box display="flex" justifyContent="flex-end" mt={1} mb={2}>
            <Link href="/reset-password" style={{ color: '#1976d2', fontSize: '14px', textDecoration: 'none' }}>
              Forgot password?
            </Link>
          </Box>
          <Button type="submit" variant="contained" color="primary" fullWidth sx={{ mt: 2 }}>
            Continue
          </Button>
        </form>
        <Divider sx={{ my: 3 }}>or</Divider>
        <Button
          variant="outlined"
          color="primary"
          fullWidth
          startIcon={<ChromeIcon className="w-5 h-5 mr-2" />}
          onClick={signInWithGoogle}
        >
          Sign in with Google
        </Button>
        <Typography align="center" mt={3} color="text.secondary" fontSize={14}>
          No account?{' '}
          <Link href="/sign-up" style={{ color: '#1976d2', fontWeight: 500 }}>
            Sign up
          </Link>
        </Typography>
      </Paper>
    </Box>
  );
}

